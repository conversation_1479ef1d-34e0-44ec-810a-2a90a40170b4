#!/usr/bin/env python3
"""
测试科研规范模式
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('.')

from data.kmeans_isolation_outlier_detection import kmeans_isolation_forest_outlier_detection

def test_research_mode():
    """测试科研规范模式"""
    
    print("🔬 测试科研规范模式")
    print("=" * 50)
    
    # 配置参数
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    train_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train"
    val_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"
    
    print(f"📋 使用特征: {behavioral_features}")
    print(f"📂 训练数据路径: {train_path}")
    print(f"📂 验证数据路径: {val_path}")
    
    # 检查数据路径
    if not os.path.exists(train_path):
        print(f"❌ 训练数据路径不存在: {train_path}")
        return
    
    if not os.path.exists(val_path):
        print(f"❌ 验证数据路径不存在: {val_path}")
        return
    
    # 模拟选择1 (科研规范模式)
    choice = "1"
    print(f"🔍 选择的处理方式: {choice} (科研规范模式)")
    
    # 训练集异常检测
    print(f"\n🔍 处理训练集...")
    train_results = kmeans_isolation_forest_outlier_detection(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        contamination=0.08,
        output_csv="train_outliers.csv"
    )
    
    # 验证集处理 - 科研规范模式
    if choice == "1":
        print(f"\n📋 科研规范模式: 验证集保持原始分布")
        print(f"💡 这种方式最符合科研规范，能更真实地评估模型泛化能力")
        val_results = None
        
        # 创建空的验证集异常文件，确保数据加载器正常工作
        import pandas as pd
        pd.DataFrame({"文件": []}).to_csv("data/val_outliers.csv", index=False)
        print(f"📄 已创建空的验证集异常文件: data/val_outliers.csv")
    
    # 汇总结果
    print("\n📊 最终结果汇总")
    print("=" * 50)
    
    if train_results:
        print(f"训练集:")
        print(f"  总文件数: {len(train_results['filenames'])}")
        print(f"  K-means异常: {len(train_results['kmeans_outliers'])}")
        print(f"  Isolation Forest异常: {len(train_results['iso_outliers'])}")
        print(f"  最终异常: {len(train_results['final_outliers'])}")
        print(f"  异常比例: {len(train_results['final_outliers'])/len(train_results['filenames'])*100:.1f}%")
        
        print(f"\n验证集:")
        print(f"  处理方式: 保持原始分布 (科研规范模式)")
        print(f"  异常文件: 0 (未进行筛选)")
        
        print(f"\n📄 输出文件:")
        print(f"  训练集异常列表: data/train_outliers.csv")
        print(f"  验证集异常列表: data/val_outliers.csv (空文件)")
        print(f"  训练集详细分析: data/train_outliers_detailed.csv")
    
    print("\n✅ 科研规范模式测试完成!")
    print("\n🔬 科研规范建议:")
    print("   ✅ 当前使用最符合科研规范的方式")
    print("   ✅ 验证集保持原始分布，能更真实评估泛化能力")
    print("   📝 论文中可以报告: '仅对训练集进行异常检测以提高数据质量'")
    
    print("\n💡 通用建议:")
    print("   1. 详细分析被移除样本的特征分布")
    print("   2. 进行消融实验对比筛选前后的模型性能")
    print("   3. 在论文中透明地报告数据预处理过程")
    print("   4. 考虑在测试集上评估最终模型时不进行异常检测")

if __name__ == "__main__":
    test_research_mode()
