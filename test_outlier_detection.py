#!/usr/bin/env python3
"""
异常检测功能测试脚本
用于验证K-means + Isolation Forest二次筛选功能
"""

import os
import sys
import pandas as pd
import numpy as np
from data.kmeans_outlier_Detection import kmeans_isolation_forest_filter, global_kmeans_filter

def create_test_data(output_dir="test_data", num_files=50):
    """创建测试数据"""
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📁 创建测试数据到: {output_dir}")
    
    # 设置随机种子
    np.random.seed(42)
    
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    
    for i in range(num_files):
        # 创建正常数据
        if i < num_files * 0.8:  # 80%正常数据
            data = {
                "pose_Tx": np.random.normal(0, 1, 280),
                "pose_Ty": np.random.normal(0, 1, 280),
                "AU17_r": np.random.normal(0.5, 0.2, 280),
                "AU26_r": np.random.normal(0.3, 0.15, 280)
            }
        else:  # 20%异常数据
            data = {
                "pose_Tx": np.random.normal(5, 2, 280),  # 异常的位置
                "pose_Ty": np.random.normal(-3, 2, 280),
                "AU17_r": np.random.normal(2, 0.5, 280),  # 异常的AU值
                "AU26_r": np.random.normal(1.5, 0.3, 280)
            }
        
        # 添加一些噪声列
        data["frame"] = range(280)
        data["timestamp"] = np.arange(0, 280 * 0.033, 0.033)[:280]
        
        df = pd.DataFrame(data)
        df.to_csv(os.path.join(output_dir, f"test_subject_{i:03d}.csv"), index=False)
    
    print(f"✅ 创建了 {num_files} 个测试文件")
    return output_dir

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试1: 基本功能测试")
    print("-" * 40)
    
    # 创建测试数据
    test_dir = create_test_data("test_data_basic", 30)
    
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    
    try:
        # 测试原始K-means
        print("🔍 测试原始K-means检测...")
        global_kmeans_filter(
            folder_path=test_dir,
            behavioral_features=behavioral_features,
            n_clusters=3,
            distance_threshold=2.0,
            output_csv="test_kmeans_outliers.csv"
        )
        
        # 测试二次筛选
        print("🌲 测试K-means + Isolation Forest二次筛选...")
        results = kmeans_isolation_forest_filter(
            folder_path=test_dir,
            behavioral_features=behavioral_features,
            n_clusters=3,
            distance_threshold=2.0,
            contamination=0.15,
            output_csv="test_combined_outliers.csv"
        )
        
        # 验证结果
        if os.path.exists("test_kmeans_outliers.csv") and os.path.exists("test_combined_outliers.csv"):
            kmeans_df = pd.read_csv("test_kmeans_outliers.csv")
            combined_df = pd.read_csv("test_combined_outliers.csv")
            
            print(f"✅ K-means检测到异常: {len(kmeans_df)}")
            print(f"✅ 二次筛选检测到异常: {len(combined_df)}")
            print(f"✅ 基本功能测试通过")
            
            return True
        else:
            print("❌ 输出文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_parameter_sensitivity():
    """测试参数敏感性"""
    print("\n🧪 测试2: 参数敏感性测试")
    print("-" * 40)
    
    test_dir = create_test_data("test_data_params", 40)
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    
    # 测试不同的contamination参数
    contamination_values = [0.05, 0.1, 0.15, 0.2]
    results = {}
    
    for contamination in contamination_values:
        try:
            print(f"🔧 测试contamination={contamination}")
            result = kmeans_isolation_forest_filter(
                folder_path=test_dir,
                behavioral_features=behavioral_features,
                n_clusters="auto",
                distance_threshold=2.5,
                contamination=contamination,
                output_csv=f"test_param_{contamination}.csv"
            )
            
            results[contamination] = len(result['weighted_outliers'])
            
        except Exception as e:
            print(f"❌ contamination={contamination} 测试失败: {e}")
            results[contamination] = -1
    
    print("\n📊 参数敏感性测试结果:")
    for contamination, outlier_count in results.items():
        if outlier_count >= 0:
            print(f"   contamination={contamination}: {outlier_count} 个异常")
        else:
            print(f"   contamination={contamination}: 测试失败")
    
    # 验证结果合理性
    valid_results = [count for count in results.values() if count >= 0]
    if len(valid_results) >= 3:
        print("✅ 参数敏感性测试通过")
        return True
    else:
        print("❌ 参数敏感性测试失败")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试3: 边界情况测试")
    print("-" * 40)
    
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    
    # 测试1: 小数据集
    print("🔍 测试小数据集 (5个文件)...")
    small_dir = create_test_data("test_data_small", 5)
    
    try:
        result = kmeans_isolation_forest_filter(
            folder_path=small_dir,
            behavioral_features=behavioral_features,
            n_clusters="auto",
            distance_threshold=2.5,
            contamination=0.2,
            output_csv="test_small_outliers.csv"
        )
        print("✅ 小数据集测试通过")
        small_test_pass = True
    except Exception as e:
        print(f"❌ 小数据集测试失败: {e}")
        small_test_pass = False
    
    # 测试2: 空文件夹
    print("🔍 测试空文件夹...")
    empty_dir = "test_data_empty"
    os.makedirs(empty_dir, exist_ok=True)
    
    try:
        result = kmeans_isolation_forest_filter(
            folder_path=empty_dir,
            behavioral_features=behavioral_features,
            n_clusters="auto",
            distance_threshold=2.5,
            contamination=0.1,
            output_csv="test_empty_outliers.csv"
        )
        print("❌ 空文件夹测试应该失败但没有失败")
        empty_test_pass = False
    except Exception as e:
        print("✅ 空文件夹测试正确失败")
        empty_test_pass = True
    
    if small_test_pass and empty_test_pass:
        print("✅ 边界情况测试通过")
        return True
    else:
        print("❌ 边界情况测试失败")
        return False

def test_integration():
    """测试集成功能"""
    print("\n🧪 测试4: 集成功能测试")
    print("-" * 40)
    
    # 测试数据加载器集成
    try:
        from data.data_loader import load_csv_data
        
        # 创建测试数据和标签
        test_dir = create_test_data("test_data_integration", 20)
        
        # 创建测试标签文件
        labels_data = {
            "chunk": [f"test_subject_{i:03d}" for i in range(20)],
            "label": np.random.choice(["Not-Engaged", "Barely-engaged", "Engaged", "Highly-Engaged"], 20)
        }
        labels_df = pd.DataFrame(labels_data)
        labels_df.to_csv("test_labels.csv", index=False)
        
        # 创建异常文件列表
        outlier_files = [f"test_subject_{i:03d}.csv" for i in range(15, 20)]  # 最后5个作为异常
        outlier_df = pd.DataFrame({"文件": outlier_files})
        outlier_df.to_csv("test_exclude_list.csv", index=False)
        
        behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
        
        # 测试数据加载（不排除异常）
        data1, labels1 = load_csv_data(test_dir, "test_labels.csv", behavioral_features)
        
        # 测试数据加载（排除异常）
        data2, labels2 = load_csv_data(test_dir, "test_labels.csv", behavioral_features, "test_exclude_list.csv")
        
        if len(data2) < len(data1):
            print(f"✅ 集成测试通过: 排除前{len(data1)}个样本，排除后{len(data2)}个样本")
            return True
        else:
            print("❌ 集成测试失败: 异常文件未被正确排除")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    import shutil
    
    # 删除测试目录
    test_dirs = ["test_data_basic", "test_data_params", "test_data_small", 
                 "test_data_empty", "test_data_integration"]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
    
    # 删除测试文件
    test_files = [
        "test_kmeans_outliers.csv", "test_combined_outliers.csv",
        "test_combined_detailed.csv", "test_labels.csv", "test_exclude_list.csv",
        "test_small_outliers.csv", "test_empty_outliers.csv"
    ]
    
    for contamination in [0.05, 0.1, 0.15, 0.2]:
        test_files.extend([
            f"test_param_{contamination}.csv",
            f"test_param_{contamination}_detailed.csv",
            f"test_param_{contamination}_kmeans.csv",
            f"test_param_{contamination}_isolation.csv",
            f"test_param_{contamination}_union.csv",
            f"test_param_{contamination}_intersection.csv"
        ])
    
    for test_file in test_files:
        if os.path.exists(test_file):
            os.remove(test_file)
    
    print("✅ 测试文件清理完成")

def main():
    """主测试函数"""
    print("🧪 异常检测功能测试套件")
    print("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(("基本功能", test_basic_functionality()))
    test_results.append(("参数敏感性", test_parameter_sensitivity()))
    test_results.append(("边界情况", test_edge_cases()))
    test_results.append(("集成功能", test_integration()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15s}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！异常检测功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    # 清理测试文件
    cleanup_test_files()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
