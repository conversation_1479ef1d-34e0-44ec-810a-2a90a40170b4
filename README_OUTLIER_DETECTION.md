# K-means + Isolation Forest 异常检测系统

本系统在原有K-means聚类异常检测的基础上，结合Isolation Forest进行二次筛选，提高异常检测的准确性和鲁棒性。

## 🎯 主要特性

### 1. 双重异常检测
- **K-means聚类**: 基于聚类中心距离的异常检测
- **Isolation Forest**: 基于随机森林的孤立点检测
- **智能融合**: 加权组合两种方法的结果

### 2. 统计特征提取
- 均值、标准差、中位数
- 25%和75%分位数
- 数据范围（最大值-最小值）
- 自动处理缺失值和无穷值

### 3. 自适应参数选择
- 自动选择最优聚类数（基于轮廓系数）
- 可配置的异常比例阈值
- 灵活的距离阈值设置

## 📁 文件结构

```
data/
└── kmeans_isolation_outlier_detection.py  # 主要异常检测模块
run_outlier_detection_simple.py            # 简单运行脚本
README_OUTLIER_DETECTION.md               # 本说明文档
```

## 🚀 快速开始

### 方法1: 直接运行主模块

```bash
python data/kmeans_isolation_outlier_detection.py
```

### 方法2: 使用简单运行脚本

```bash
python run_outlier_detection_simple.py
```

### 方法3: 在代码中调用

```python
from data.kmeans_isolation_outlier_detection import kmeans_isolation_forest_outlier_detection

# 执行异常检测
results = kmeans_isolation_forest_outlier_detection(
    folder_path="/path/to/your/data",
    behavioral_features=["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"],
    n_clusters="auto",
    distance_threshold=2.5,
    contamination=0.08,
    output_csv="outliers.csv"
)
```

## ⚙️ 参数说明

### 主要参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `folder_path` | str | - | 包含CSV文件的文件夹路径 |
| `behavioral_features` | list | - | 要分析的行为特征列名 |
| `n_clusters` | int/"auto" | "auto" | K-means聚类数，"auto"为自动选择 |
| `distance_threshold` | float | 2.5 | K-means距离阈值倍数 |
| `contamination` | float | 0.08 | 预期异常比例（0.05-0.2） |
| `output_csv` | str | "outliers.csv" | 输出文件名 |

### 参数调优建议

- **contamination**: 
  - 0.05-0.08: 严格筛选，适合高质量要求
  - 0.08-0.12: 平衡筛选，推荐使用
  - 0.12-0.20: 宽松筛选，保留更多数据

- **distance_threshold**:
  - 2.0: 严格的K-means筛选
  - 2.5: 平衡的K-means筛选（推荐）
  - 3.0: 宽松的K-means筛选

## 📊 输出文件

### 主要输出

1. **`train_outliers.csv`** / **`val_outliers.csv`**
   - 最终的异常文件列表
   - 可直接用于数据加载器的排除列表

2. **`train_outliers_detailed.csv`** / **`val_outliers_detailed.csv`**
   - 详细的分析结果
   - 包含每个文件的异常分数和标记

### 详细分析文件字段

| 字段 | 说明 |
|------|------|
| `文件` | 文件名 |
| `kmeans_distance` | K-means聚类中心距离 |
| `kmeans_outlier` | K-means异常标记 |
| `iso_score` | Isolation Forest异常分数 |
| `iso_outlier` | Isolation Forest异常标记 |
| `combined_score` | 组合异常分数 |
| `final_outlier` | 最终异常标记 |

## 🔄 与训练流程集成

数据加载器已自动更新，会优先使用新的异常检测结果：

```python
# data/data_loader.py 会自动查找并使用以下文件（按优先级）：
# 1. train_outliers.csv (新的二次筛选结果)
# 2. train_combined_outliers.csv (备选结果)
# 3. 原始K-means结果 (向后兼容)
```

## 📈 检测流程

1. **数据加载**: 读取CSV文件并提取行为特征
2. **特征工程**: 计算统计特征（均值、标准差等）
3. **数据标准化**: 使用StandardScaler标准化特征
4. **K-means检测**: 聚类并计算到中心的距离
5. **Isolation Forest检测**: 使用随机森林检测孤立点
6. **结果融合**: 加权组合两种方法的结果
7. **输出保存**: 保存异常文件列表和详细分析

## 🛠️ 故障排除

### 常见问题

1. **路径不存在**
   ```
   ❌ 训练数据路径不存在: /path/to/data
   ```
   - 检查数据路径是否正确
   - 确认文件夹存在且包含CSV文件

2. **特征列缺失**
   ```
   ⚠️ filename.csv 缺少特征: ['AU17_r']
   ```
   - 检查CSV文件是否包含所需的特征列
   - 确认列名拼写正确

3. **内存不足**
   - 减少`n_estimators`参数（在代码中修改）
   - 处理较小的数据批次

4. **检测结果异常**
   - 调整`contamination`参数
   - 修改`distance_threshold`参数
   - 检查输入数据质量

### 性能优化

- 使用`n_jobs=-1`启用多线程处理
- 对于大数据集，可以考虑分批处理
- 调整`max_samples`参数控制内存使用

## 📞 使用示例

### 基本使用

```python
# 导入函数
from data.kmeans_isolation_outlier_detection import kmeans_isolation_forest_outlier_detection

# 配置参数
features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
data_path = "/path/to/your/csv/files"

# 执行检测
results = kmeans_isolation_forest_outlier_detection(
    folder_path=data_path,
    behavioral_features=features,
    contamination=0.08,
    output_csv="my_outliers.csv"
)

# 查看结果
print(f"检测到 {len(results['final_outliers'])} 个异常文件")
```

### 批量处理

```python
datasets = {
    "train": "/path/to/train",
    "val": "/path/to/val",
    "test": "/path/to/test"
}

for name, path in datasets.items():
    print(f"处理 {name} 数据集...")
    results = kmeans_isolation_forest_outlier_detection(
        folder_path=path,
        behavioral_features=features,
        output_csv=f"{name}_outliers.csv"
    )
```

## 🎯 最佳实践

1. **首次使用**: 从默认参数开始，观察检测结果
2. **参数调优**: 根据数据特点调整contamination和distance_threshold
3. **结果验证**: 查看详细分析文件，确认异常检测合理
4. **持续监控**: 定期重新运行检测，确保数据质量

---

**注意**: 本系统设计为数据预处理工具，建议在模型训练前运行，以提高训练数据质量。
