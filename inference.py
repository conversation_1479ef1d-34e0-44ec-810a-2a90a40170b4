import json  # 导入json模块，用于处理JSON文件
import torch  # 导入PyTorch库，用于深度学习任务
import random  # 导入random模块，用于设置随机种子
import numpy as np  # 导入numpy库，用于数值计算
import pandas as pd  # 导入pandas库，用于数据处理和分析
from torch import nn  # 从PyTorch中导入神经网络模块

from models.feature_fusion import Decision_Fusion  # 从models模块中导入Decision_Fusion模型
from test import evaluate  # 从test模块中导入evaluate函数，用于模型评估
from data.data_loader import get_source_data_inference  # 从data_loader模块中导入函数，用于加载推理数据
from utilities.plotting import plot_all_confusion_matrices

gpus = [0]  # 指定使用的GPU设备

def inference(n_classes, behavioral_features, target_mean, target_std, inference_folder_path, label_file_inference,
              sampling_frequency, freq_min, freq_max, tensor_height, model_weights_path):
    """
    加载预训练模型权重并在推理数据上进行评估。

    参数:
        n_classes (int): 分类任务的类别数量。
        behavioral_features (list of str): 使用的行为特征列表。
        target_mean (float): 训练数据的均值，用于标准化。
        target_std (float): 训练数据的标准差，用于标准化。
        inference_folder_path (str): 包含推理数据的文件夹路径。
        label_file_inference (str): 包含标签的CSV文件路径。
        sampling_frequency (int): 信号的采样频率。
        freq_min (float): 连续小波变换（CWT）的最小频率。
        freq_max (float): 连续小波变换（CWT）的最大频率。
        tensor_height (int): CWT的离散频率数量。
        model_weights_path (str): 预训练模型权重的路径。

    返回:
        tuple: 包含推理标签和预测标签的元组。
    """

    # 设置设备为GPU（如果可用），否则使用CPU
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 初始化模型并加载预训练权重
    model = Decision_Fusion(n_classes)  # 创建Decision_Fusion模型的实例
    model = nn.DataParallel(model)  # 将模型包装为多GPU训练模式
    model = model.to(device)  # 将模型移动到指定设备（GPU或CPU）
    model.load_state_dict(torch.load(model_weights_path))  # 加载预训练模型权重
    # model.load_state_dict(torch.load(model_weights_path, map_location=torch.device(device)))
    model.eval()  # 将模型设置为评估模式

    # 从推理目录加载数据
    inference_signal_data, inference_label = get_source_data_inference(
        inference_folder_path, label_file_inference, behavioral_features, target_mean, target_std)

    # 将数据转换为PyTorch张量并移动到指定设备
    inference_signal_data = torch.from_numpy(inference_signal_data).float().to(device)
    inference_label = torch.from_numpy(inference_label).long().to(device)

    # 初始化损失函数
    criterion_cls = torch.nn.CrossEntropyLoss().to(device)  # 使用交叉熵损失函数

    # 在推理数据上评估模型
    inference_acc, loss_inference, y_pred = evaluate(model, inference_signal_data, inference_label, criterion_cls,
                                                     freq_min, freq_max, tensor_height, sampling_frequency)

    print(f'推理准确率: {inference_acc}')  # 打印推理准确率
    print(f'推理损失: {loss_inference}')  # 打印推理损失

    return inference_label, y_pred  # 返回推理标签和预测标签


def load_config(config_path):
    """
    加载配置文件。

    参数:
        config_path (str): 配置文件的路径。

    返回:
        dict: 包含配置信息的字典。
    """
    with open(config_path, 'r') as f:
        config = json.load(f)  # 读取并解析JSON格式的配置文件
    return config


# 设置随机种子以确保结果可复现
seed_n = 42
random.seed(seed_n)  # 设置Python随机种子
np.random.seed(seed_n)  # 设置numpy随机种子
torch.manual_seed(seed_n)  # 设置PyTorch随机种子
torch.cuda.manual_seed(seed_n)  # 设置当前GPU的随机种子
torch.cuda.manual_seed_all(seed_n)  # 设置所有GPU的随机种子
torch.backends.cudnn.deterministic = True  # 确保CUDA操作是确定性的
torch.backends.cudnn.benchmark = False  # 禁用CUDA的自动优化

if __name__ == "__main__":
    # 加载配置文件
    config = load_config('config.json')  # 从config.json文件中加载配置

    # 运行推理以获取真实标签和预测标签
    y_true, y_pred = inference(
        n_classes=config['n_classes'],
        behavioral_features=config['behavioral_features'],
        target_mean=config['target_mean'],
        target_std=config['target_std'],
        inference_folder_path=config['inference_folder_path'],
        label_file_inference=config['label_file_inference'],
        sampling_frequency=config['sampling_frequency'],
        freq_min=config['freq_min'],
        freq_max=config['freq_max'],
        tensor_height=config['tensor_height'],
        model_weights_path=config['final_model_weights']
    )

    # 生成推理混淆矩阵
    engagement_classes = ['Not-Engaged', 'Barely-engaged', 'Engaged', 'Highly-Engaged']
    plot_all_confusion_matrices(y_true, y_pred, engagement_classes)

    # 创建一个包含真实标签和预测标签的DataFrame
    results_df = pd.DataFrame({
        '真实标签': y_true.cpu().numpy(),  # 将真实标签从GPU移动到CPU并转换为numpy数组
        '预测标签': y_pred.cpu().numpy()  # 将预测标签从GPU移动到CPU并转换为numpy数组
    })

    # 将结果保存到CSV文件中
    results_df.to_csv('inference_results.csv', index=False)  # 保存DataFrame为CSV文件，不包含索引
    print('\n结果已保存到 inference_results.csv')  # 打印保存成功的提示信息
