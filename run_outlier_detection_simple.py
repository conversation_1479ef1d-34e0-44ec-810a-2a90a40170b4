#!/usr/bin/env python3
"""
简单的异常检测运行脚本
直接调用K-means + Isolation Forest二次筛选功能
"""

import os
import sys

# 添加data目录到Python路径
sys.path.append('data')

def main():
    """主函数"""
    print("🚀 运行K-means + Isolation Forest异常检测")
    print("=" * 60)
    
    try:
        # 导入异常检测函数
        from kmeans_isolation_outlier_detection import kmeans_isolation_forest_outlier_detection
        
        # 配置参数
        behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
        
        # 数据路径
        train_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train"
        val_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"
        
        print(f"📋 使用特征: {behavioral_features}")
        print(f"📂 训练数据: {train_path}")
        print(f"📂 验证数据: {val_path}")
        
        # 检查路径
        if not os.path.exists(train_path):
            print(f"❌ 训练数据路径不存在: {train_path}")
            print("请检查数据路径配置")
            return False
        
        if not os.path.exists(val_path):
            print(f"❌ 验证数据路径不存在: {val_path}")
            print("请检查数据路径配置")
            return False
        
        # 执行训练集异常检测
        print(f"\n🔍 处理训练集...")
        train_results = kmeans_isolation_forest_outlier_detection(
            folder_path=train_path,
            behavioral_features=behavioral_features,
            n_clusters="auto",
            distance_threshold=2.5,
            contamination=0.08,
            output_csv="train_outliers.csv"
        )
        
        if not train_results:
            print("❌ 训练集异常检测失败")
            return False
        
        # 执行验证集异常检测
        print(f"\n🔍 处理验证集...")
        val_results = kmeans_isolation_forest_outlier_detection(
            folder_path=val_path,
            behavioral_features=behavioral_features,
            n_clusters="auto",
            distance_threshold=2.5,
            contamination=0.08,
            output_csv="val_outliers.csv"
        )
        
        if not val_results:
            print("❌ 验证集异常检测失败")
            return False
        
        # 显示结果摘要
        print("\n📊 检测结果摘要")
        print("=" * 60)
        
        print(f"训练集:")
        print(f"  总样本数: {len(train_results['filenames'])}")
        print(f"  异常样本数: {len(train_results['final_outliers'])}")
        print(f"  异常比例: {len(train_results['final_outliers'])/len(train_results['filenames'])*100:.1f}%")
        
        print(f"\n验证集:")
        print(f"  总样本数: {len(val_results['filenames'])}")
        print(f"  异常样本数: {len(val_results['final_outliers'])}")
        print(f"  异常比例: {len(val_results['final_outliers'])/len(val_results['filenames'])*100:.1f}%")
        
        print(f"\n📄 输出文件:")
        print(f"  train_outliers.csv - 训练集异常文件列表")
        print(f"  val_outliers.csv - 验证集异常文件列表")
        print(f"  train_outliers_detailed.csv - 训练集详细分析")
        print(f"  val_outliers_detailed.csv - 验证集详细分析")
        
        print(f"\n✅ 异常检测完成!")
        print(f"💡 现在可以在训练时使用这些异常文件列表来排除异常样本")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保data/kmeans_isolation_outlier_detection.py文件存在")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 异常检测成功完成!")
    else:
        print("\n💥 异常检测失败，请检查错误信息")
    
    sys.exit(0 if success else 1)
