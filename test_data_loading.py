#!/usr/bin/env python3
"""
测试数据加载器是否正确使用异常检测结果
"""

import os
import sys
import json

def test_data_loading():
    """测试数据加载功能"""
    print("🧪 测试数据加载器异常文件排除功能")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from data.data_loader import get_source_data
        
        # 加载配置
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # 获取配置参数
        train_folder_path = config['train_folder_path']
        test_folder_path = config['test_folder_path']
        label_file = config['label_file']
        behavioral_features = config['behavioral_features']
        
        print(f"📂 训练数据路径: {train_folder_path}")
        print(f"📂 测试数据路径: {test_folder_path}")
        print(f"📋 行为特征: {behavioral_features}")
        
        # 检查异常检测文件是否存在
        outlier_files = {
            "训练集异常文件": "data/train_outliers.csv",
            "验证集异常文件": "data/val_outliers.csv",
            "训练集详细分析": "data/train_outliers_detailed.csv",
            "验证集详细分析": "data/val_outliers_detailed.csv"
        }
        
        print(f"\n📄 检查异常检测文件:")
        for name, path in outlier_files.items():
            if os.path.exists(path):
                print(f"  ✅ {name}: {path}")
            else:
                print(f"  ❌ {name}: {path} (不存在)")
        
        # 测试数据加载
        print(f"\n🔄 测试数据加载...")
        train_data, train_labels, test_data, test_labels = get_source_data(
            train_folder_path, test_folder_path, label_file, behavioral_features
        )
        
        print(f"\n📊 数据加载结果:")
        print(f"  训练数据形状: {train_data.shape}")
        print(f"  训练标签形状: {train_labels.shape}")
        print(f"  测试数据形状: {test_data.shape}")
        print(f"  测试标签形状: {test_labels.shape}")
        
        # 统计标签分布
        import numpy as np
        unique_train, counts_train = np.unique(train_labels, return_counts=True)
        unique_test, counts_test = np.unique(test_labels, return_counts=True)
        
        print(f"\n📈 标签分布:")
        print(f"  训练集标签分布: {dict(zip(unique_train, counts_train))}")
        print(f"  测试集标签分布: {dict(zip(unique_test, counts_test))}")
        
        print(f"\n✅ 数据加载测试成功!")
        print(f"💡 数据加载器已自动使用异常检测结果排除异常样本")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_outlier_files():
    """检查异常检测文件的详细信息"""
    print(f"\n📋 异常检测文件详细信息:")
    print("-" * 40)
    
    files_to_check = [
        ("训练集异常文件", "data/train_outliers.csv"),
        ("验证集异常文件", "data/val_outliers.csv")
    ]
    
    for name, filepath in files_to_check:
        if os.path.exists(filepath):
            try:
                import pandas as pd
                df = pd.read_csv(filepath)
                print(f"  {name}:")
                print(f"    文件路径: {filepath}")
                print(f"    异常文件数量: {len(df)}")
                print(f"    前5个异常文件: {list(df['文件'].head())}")
            except Exception as e:
                print(f"  {name}: 读取失败 - {e}")
        else:
            print(f"  {name}: 文件不存在 - {filepath}")

def main():
    """主函数"""
    print("🔍 数据加载器集成测试")
    print("=" * 60)
    
    # 检查异常检测文件
    check_outlier_files()
    
    # 测试数据加载
    success = test_data_loading()
    
    if success:
        print(f"\n🎉 集成测试成功!")
        print(f"💡 现在可以直接运行 main.py 进行训练，系统会自动排除异常样本")
        print(f"📝 建议的运行命令:")
        print(f"   python main.py")
    else:
        print(f"\n💥 集成测试失败!")
        print(f"🔧 请检查:")
        print(f"   1. 异常检测文件是否存在")
        print(f"   2. 数据路径配置是否正确")
        print(f"   3. 依赖包是否安装完整")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
