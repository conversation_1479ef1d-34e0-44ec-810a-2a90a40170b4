#!/usr/bin/env python3
"""
运行异常检测的主脚本
结合K-means和Isolation Forest进行数据预处理和异常检测
"""

import os
import sys
import pandas as pd
from data.kmeans_outlier_Detection import kmeans_isolation_forest_filter, global_kmeans_filter

def main():
    """主函数：执行异常检测流程"""
    
    print("🚀 TCCT-Net 数据异常检测系统")
    print("=" * 60)
    
    # 配置参数
    behavioral_features = [
        "pose_Tx", "pose_Ty",           # 头部位置特征
        "AU17_r", "AU26_r"             # 面部动作单元特征
    ]
    
    # 数据路径配置
    train_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train"
    val_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"
    
    # 检查数据路径是否存在
    if not os.path.exists(train_path):
        print(f"❌ 训练数据路径不存在: {train_path}")
        print("请检查数据路径配置")
        return
    
    if not os.path.exists(val_path):
        print(f"❌ 验证数据路径不存在: {val_path}")
        print("请检查数据路径配置")
        return
    
    print(f"📂 训练数据路径: {train_path}")
    print(f"📂 验证数据路径: {val_path}")
    print(f"📋 使用特征: {behavioral_features}")
    
    # 选择检测方法
    print("\n🔧 选择异常检测方法:")
    print("1. 仅使用K-means聚类检测")
    print("2. 使用K-means + Isolation Forest二次筛选 (推荐)")
    print("3. 运行两种方法并对比结果")
    
    choice = input("请选择 (1/2/3) [默认: 2]: ").strip()
    if not choice:
        choice = "2"
    
    try:
        if choice == "1":
            run_kmeans_only(train_path, val_path, behavioral_features)
        elif choice == "2":
            run_combined_detection(train_path, val_path, behavioral_features)
        elif choice == "3":
            run_comparison(train_path, val_path, behavioral_features)
        else:
            print("❌ 无效选择，使用默认方法 (K-means + Isolation Forest)")
            run_combined_detection(train_path, val_path, behavioral_features)
            
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def run_kmeans_only(train_path, val_path, behavioral_features):
    """仅使用K-means聚类检测"""
    print("\n📊 执行K-means聚类异常检测")
    print("-" * 40)
    
    # 训练集
    print("🔍 处理训练集...")
    global_kmeans_filter(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        output_csv="train_kmeans_outliers.csv"
    )
    
    # 验证集
    print("🔍 处理验证集...")
    global_kmeans_filter(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        output_csv="val_kmeans_outliers.csv"
    )
    
    print("\n✅ K-means异常检测完成!")
    print("📄 结果文件: train_kmeans_outliers.csv, val_kmeans_outliers.csv")

def run_combined_detection(train_path, val_path, behavioral_features):
    """使用K-means + Isolation Forest二次筛选"""
    print("\n🌲 执行K-means + Isolation Forest二次筛选")
    print("-" * 50)
    
    # 训练集
    print("🔍 处理训练集...")
    train_results = kmeans_isolation_forest_filter(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        contamination=0.08,  # 预期8%的异常比例
        output_csv="train_combined_outliers.csv"
    )
    
    # 验证集
    print("🔍 处理验证集...")
    val_results = kmeans_isolation_forest_filter(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        contamination=0.08,  # 预期8%的异常比例
        output_csv="val_combined_outliers.csv"
    )
    
    print("\n✅ 二次筛选异常检测完成!")
    print("📄 主要结果文件: train_combined_outliers.csv, val_combined_outliers.csv")
    print("📄 详细分析文件: train_combined_detailed.csv, val_combined_detailed.csv")
    print("💡 建议在训练时使用主要结果文件作为排除列表")

def run_comparison(train_path, val_path, behavioral_features):
    """运行两种方法并对比结果"""
    print("\n📊 运行方法对比分析")
    print("-" * 40)
    
    # 先运行K-means
    print("1️⃣ 执行K-means检测...")
    run_kmeans_only(train_path, val_path, behavioral_features)
    
    # 再运行二次筛选
    print("\n2️⃣ 执行二次筛选...")
    run_combined_detection(train_path, val_path, behavioral_features)
    
    # 对比分析
    print("\n📈 结果对比分析")
    print("=" * 40)
    
    try:
        # 读取结果文件
        train_kmeans = pd.read_csv("train_kmeans_outliers.csv")
        val_kmeans = pd.read_csv("val_kmeans_outliers.csv")
        train_combined = pd.read_csv("train_combined_outliers.csv")
        val_combined = pd.read_csv("val_combined_outliers.csv")
        
        print(f"训练集对比:")
        print(f"  K-means异常数: {len(train_kmeans)}")
        print(f"  二次筛选异常数: {len(train_combined)}")
        reduction_train = (len(train_kmeans) - len(train_combined)) / len(train_kmeans) * 100 if len(train_kmeans) > 0 else 0
        print(f"  异常数减少: {reduction_train:.1f}%")
        
        print(f"\n验证集对比:")
        print(f"  K-means异常数: {len(val_kmeans)}")
        print(f"  二次筛选异常数: {len(val_combined)}")
        reduction_val = (len(val_kmeans) - len(val_combined)) / len(val_kmeans) * 100 if len(val_kmeans) > 0 else 0
        print(f"  异常数减少: {reduction_val:.1f}%")
        
        # 分析重叠情况
        train_kmeans_set = set(train_kmeans['文件'].values)
        train_combined_set = set(train_combined['文件'].values)
        train_overlap = len(train_kmeans_set & train_combined_set)
        
        val_kmeans_set = set(val_kmeans['文件'].values)
        val_combined_set = set(val_combined['文件'].values)
        val_overlap = len(val_kmeans_set & val_combined_set)
        
        print(f"\n重叠分析:")
        print(f"  训练集两种方法共同识别的异常: {train_overlap}")
        print(f"  验证集两种方法共同识别的异常: {val_overlap}")
        
        # 保存对比报告
        comparison_report = {
            "数据集": ["训练集", "验证集"],
            "K-means异常数": [len(train_kmeans), len(val_kmeans)],
            "二次筛选异常数": [len(train_combined), len(val_combined)],
            "减少比例(%)": [reduction_train, reduction_val],
            "方法重叠数": [train_overlap, val_overlap]
        }
        
        comparison_df = pd.DataFrame(comparison_report)
        comparison_df.to_csv("outlier_detection_comparison.csv", index=False)
        print(f"\n📄 对比报告已保存: outlier_detection_comparison.csv")
        
    except Exception as e:
        print(f"⚠️ 对比分析失败: {e}")
    
    print("\n💡 建议:")
    print("   - 如果追求更严格的数据质量，使用二次筛选结果")
    print("   - 如果担心过度筛选，可以使用K-means结果")
    print("   - 可以查看详细分析文件来了解具体的异常模式")

if __name__ == "__main__":
    main()
