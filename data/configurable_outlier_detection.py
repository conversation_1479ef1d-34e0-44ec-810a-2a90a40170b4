#!/usr/bin/env python3
"""
可配置的异常检测系统
支持通过配置文件自定义参数，结合K-means和Isolation Forest
"""

import os
import json
import pandas as pd
import numpy as np
import logging
from tqdm import tqdm
from sklearn.cluster import KMeans
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class ConfigurableOutlierDetector:
    """可配置的异常检测器"""
    
    def __init__(self, config_path="outlier_detection_config.json"):
        """初始化检测器"""
        self.config = self.load_config(config_path)
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
    def load_config(self, config_path):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            print("使用默认配置...")
            return self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "behavioral_features": ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"],
            "kmeans_params": {"n_clusters": "auto", "distance_threshold": 2.5},
            "isolation_forest_params": {"contamination": 0.08, "n_estimators": 200},
            "combination_strategy": {"method": "weighted", "kmeans_weight": 0.6, "isolation_weight": 0.4},
            "output_settings": {"save_detailed_results": True, "output_directory": "outlier_analysis"}
        }
    
    def setup_logging(self):
        """设置日志"""
        log_config = self.config.get("logging", {})
        log_level = getattr(logging, log_config.get("log_level", "INFO"))
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_config.get("log_file", "outlier_detection.log"))
            ] if log_config.get("save_logs", True) else [logging.StreamHandler()]
        )
    
    def extract_features(self, df, behavioral_features):
        """提取特征向量"""
        feature_vector = []
        
        if self.config.get("advanced_features", {}).get("use_statistical_features", True):
            # 使用统计特征
            stat_features = self.config.get("advanced_features", {}).get("statistical_features", 
                                          ["mean", "std", "median", "q25", "q75", "range"])
            
            for col in behavioral_features:
                if col not in df.columns:
                    continue
                    
                col_data = df[col].values
                
                if "mean" in stat_features:
                    feature_vector.append(np.mean(col_data))
                if "std" in stat_features:
                    feature_vector.append(np.std(col_data))
                if "median" in stat_features:
                    feature_vector.append(np.median(col_data))
                if "q25" in stat_features:
                    feature_vector.append(np.percentile(col_data, 25))
                if "q75" in stat_features:
                    feature_vector.append(np.percentile(col_data, 75))
                if "range" in stat_features:
                    feature_vector.append(np.max(col_data) - np.min(col_data))
        else:
            # 使用原始特征展平
            for col in behavioral_features:
                if col in df.columns:
                    feature_vector.extend(df[col].values)
        
        return feature_vector
    
    def load_data(self, folder_path, behavioral_features):
        """加载数据"""
        all_vectors = []
        filenames = []
        
        self.logger.info(f"开始加载数据: {folder_path}")
        
        for fname in tqdm(os.listdir(folder_path), desc="加载数据"):
            if not fname.endswith(".csv"):
                continue
                
            csv_path = os.path.join(folder_path, fname)
            try:
                df = pd.read_csv(csv_path)
                
                # 检查必要的列是否存在
                missing_cols = [col for col in behavioral_features if col not in df.columns]
                if missing_cols:
                    self.logger.warning(f"文件 {fname} 缺少列: {missing_cols}")
                    continue
                
                # 清理数据
                df = df[behavioral_features].replace([np.inf, -np.inf], np.nan).dropna()
                if df.shape[0] == 0:
                    self.logger.warning(f"文件 {fname} 清理后无有效数据")
                    continue
                
                # 提取特征
                feature_vector = self.extract_features(df, behavioral_features)
                if len(feature_vector) > 0:
                    all_vectors.append(feature_vector)
                    filenames.append(fname)
                    
            except Exception as e:
                self.logger.error(f"读取文件失败 {fname}: {e}")
        
        if not all_vectors:
            raise ValueError(f"没有可用数据: {folder_path}")
        
        # 确保所有向量长度一致
        min_len = min(len(v) for v in all_vectors)
        all_vectors = [v[:min_len] for v in all_vectors]
        
        X = np.array(all_vectors)
        self.logger.info(f"数据加载完成: {X.shape}, 文件数: {len(filenames)}")
        
        return X, filenames
    
    def kmeans_detection(self, X, filenames):
        """K-means异常检测"""
        params = self.config.get("kmeans_params", {})
        n_clusters = params.get("n_clusters", "auto")
        distance_threshold = params.get("distance_threshold", 2.5)
        
        self.logger.info("开始K-means异常检测")
        
        if n_clusters == "auto":
            # 自动选择聚类数
            max_k = min(10, len(filenames) // 5)
            if max_k < 2:
                max_k = 2
            
            best_score = -1
            best_k = 2
            
            for k in range(2, max_k + 1):
                kmeans_k = KMeans(n_clusters=k, random_state=params.get("random_state", 42)).fit(X)
                score = silhouette_score(X, kmeans_k.labels_)
                if score > best_score:
                    best_score = score
                    best_k = k
            
            n_clusters = best_k
            self.logger.info(f"自动选择聚类数: {n_clusters}, 轮廓系数: {best_score:.3f}")
        
        # 执行聚类
        kmeans = KMeans(
            n_clusters=n_clusters,
            random_state=params.get("random_state", 42),
            n_init=params.get("n_init", 10),
            max_iter=params.get("max_iter", 300)
        ).fit(X)
        
        # 计算异常
        distances = np.linalg.norm(X - kmeans.cluster_centers_[kmeans.labels_], axis=1)
        threshold = np.mean(distances) + distance_threshold * np.std(distances)
        outliers = [f for f, d in zip(filenames, distances) if d > threshold]
        
        self.logger.info(f"K-means检测到异常: {len(outliers)}/{len(filenames)}")
        
        return outliers, distances, kmeans
    
    def isolation_forest_detection(self, X, filenames):
        """Isolation Forest异常检测"""
        params = self.config.get("isolation_forest_params", {})
        
        self.logger.info("开始Isolation Forest异常检测")
        
        iso_forest = IsolationForest(
            contamination=params.get("contamination", 0.08),
            n_estimators=params.get("n_estimators", 200),
            max_samples=params.get("max_samples", "auto"),
            random_state=params.get("random_state", 42),
            n_jobs=params.get("n_jobs", -1)
        )
        
        predictions = iso_forest.fit_predict(X)
        scores = iso_forest.decision_function(X)
        outliers = [f for f, pred in zip(filenames, predictions) if pred == -1]
        
        self.logger.info(f"Isolation Forest检测到异常: {len(outliers)}/{len(filenames)}")
        
        return outliers, scores, iso_forest
    
    def combine_results(self, filenames, kmeans_outliers, iso_outliers, 
                       kmeans_distances, iso_scores):
        """组合检测结果"""
        strategy = self.config.get("combination_strategy", {})
        method = strategy.get("method", "weighted")
        
        self.logger.info(f"使用组合策略: {method}")
        
        if method == "union":
            combined_outliers = list(set(kmeans_outliers) | set(iso_outliers))
        elif method == "intersection":
            combined_outliers = list(set(kmeans_outliers) & set(iso_outliers))
        elif method == "weighted":
            # 标准化分数
            norm_distances = (kmeans_distances - kmeans_distances.min()) / (kmeans_distances.max() - kmeans_distances.min())
            norm_scores = (iso_scores.max() - iso_scores) / (iso_scores.max() - iso_scores.min())
            
            # 加权组合
            kmeans_weight = strategy.get("kmeans_weight", 0.6)
            iso_weight = strategy.get("isolation_weight", 0.4)
            combined_scores = kmeans_weight * norm_distances + iso_weight * norm_scores
            
            # 确定阈值
            contamination = self.config.get("isolation_forest_params", {}).get("contamination", 0.08)
            threshold = np.percentile(combined_scores, 100 * (1 - contamination))
            combined_outliers = [f for f, score in zip(filenames, combined_scores) if score > threshold]
        else:
            self.logger.warning(f"未知组合方法: {method}, 使用加权方法")
            combined_outliers = list(set(kmeans_outliers) | set(iso_outliers))
        
        self.logger.info(f"组合结果异常数: {len(combined_outliers)}")
        
        return combined_outliers
    
    def detect_outliers(self, folder_path, output_prefix="outliers"):
        """执行完整的异常检测流程"""
        behavioral_features = self.config.get("behavioral_features", [])
        
        # 加载数据
        X, filenames = self.load_data(folder_path, behavioral_features)
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # K-means检测
        kmeans_outliers, kmeans_distances, kmeans_model = self.kmeans_detection(X_scaled, filenames)
        
        # Isolation Forest检测
        iso_outliers, iso_scores, iso_model = self.isolation_forest_detection(X_scaled, filenames)
        
        # 组合结果
        combined_outliers = self.combine_results(filenames, kmeans_outliers, iso_outliers,
                                               kmeans_distances, iso_scores)
        
        # 保存结果
        self.save_results(filenames, kmeans_outliers, iso_outliers, combined_outliers,
                         kmeans_distances, iso_scores, output_prefix)
        
        return {
            'filenames': filenames,
            'X_scaled': X_scaled,
            'kmeans_outliers': kmeans_outliers,
            'iso_outliers': iso_outliers,
            'combined_outliers': combined_outliers,
            'kmeans_distances': kmeans_distances,
            'iso_scores': iso_scores
        }
    
    def save_results(self, filenames, kmeans_outliers, iso_outliers, combined_outliers,
                    kmeans_distances, iso_scores, output_prefix):
        """保存检测结果"""
        output_settings = self.config.get("output_settings", {})
        
        # 保存主要结果
        pd.DataFrame({"文件": combined_outliers}).to_csv(f"{output_prefix}.csv", index=False)
        
        # 保存详细结果
        if output_settings.get("save_detailed_results", True):
            detailed_df = pd.DataFrame({
                "文件": filenames,
                "kmeans_distance": kmeans_distances,
                "kmeans_outlier": [f in kmeans_outliers for f in filenames],
                "iso_score": iso_scores,
                "iso_outlier": [f in iso_outliers for f in filenames],
                "combined_outlier": [f in combined_outliers for f in filenames]
            })
            detailed_df.to_csv(f"{output_prefix}_detailed.csv", index=False)
        
        self.logger.info(f"结果已保存: {output_prefix}.csv")

def main():
    """主函数"""
    print("🔧 可配置异常检测系统")
    print("=" * 50)
    
    # 初始化检测器
    detector = ConfigurableOutlierDetector("outlier_detection_config.json")
    
    # 获取数据路径
    data_paths = detector.config.get("data_paths", {})
    train_path = data_paths.get("train_folder")
    val_path = data_paths.get("validation_folder")
    
    # 检测训练集
    if train_path and os.path.exists(train_path):
        print(f"🔍 处理训练集: {train_path}")
        train_results = detector.detect_outliers(train_path, "train_configurable_outliers")
    else:
        print(f"⚠️ 训练集路径无效: {train_path}")
    
    # 检测验证集
    if val_path and os.path.exists(val_path):
        print(f"🔍 处理验证集: {val_path}")
        val_results = detector.detect_outliers(val_path, "val_configurable_outliers")
    else:
        print(f"⚠️ 验证集路径无效: {val_path}")
    
    print("✅ 可配置异常检测完成!")

if __name__ == "__main__":
    main()
