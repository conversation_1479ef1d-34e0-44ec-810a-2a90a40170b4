import cv2
import os

def extract_frames_every_2s(video_path, output_dir):
    """
    从视频中每隔2秒提取一帧并保存
    
    参数:
        video_path: 输入视频文件路径
        output_dir: 输出目录路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    
    # 获取视频帧率
    fps = cap.get(cv2.CAP_PROP_FPS)
    if fps == 0:
        print("无法获取视频帧率")
        return
    
    # 计算每隔2秒对应的帧间隔
    frame_interval = int(fps * 2)
    
    # 获取视频总帧数和时长
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    print(f"视频时长: {duration:.2f}秒, 总帧数: {total_frames}, 帧率: {fps:.2f}")
    
    frame_count = 0
    saved_count = 0
    
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        # 每隔frame_interval帧保存一次
        if frame_count % frame_interval == 0:
            # 计算当前时间(秒)
            current_time = frame_count / fps
            
            # 生成输出文件名
            output_path = os.path.join(output_dir, f"frame_{current_time:.1f}s.jpg")
            cv2.imwrite(output_path, frame)
            print(f"已保存: {output_path}")
            saved_count += 1
        
        frame_count += 1
    
    cap.release()
    print(f"处理完成，共保存{saved_count}帧图像")

# 使用示例
if __name__ == "__main__":
    video_file = "/data/gsd/ywj/dataset/subject_2_thw33jke4j_vid_1_29.mp4"  # 替换为你的视频文件路径
    output_directory = "/data/gsd/ywj/dataset/subject_2_thw33jke4j_vid_1_29"  # 输出目录
    
    extract_frames_every_2s(video_file, output_directory)