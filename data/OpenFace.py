import os
import subprocess

# 设置 OpenFace 的 FeatureExtraction 工具路径
OPENFACE_PATH = "/data/gsd/czx/project/OpenFace-master/build/bin/FeatureExtraction"

# 输入输出路径配置（MP4 教学视频或帧图像）
INPUT_PATH = "/data/gsd/ywj/dataset/subject_2_thw33jke4j_vid_1_29/"  # 可为单个视频或包含帧图的文件夹
OUTPUT_PATH = "/data/gsd/ywj/dataset/output"  # 输出 CSV 及图像等数据文件

# 构建命令
command = [
    OPENFACE_PATH,
    "-fdir", INPUT_PATH,        # 输入为图像目录
    "-out_dir", OUTPUT_PATH,    # 输出目录
    "-2Dfp", "-3Dfp",           # 2D/3D facial landmarks
    "-aus",                     # 表情 AU
    "-pose",                    # 头部姿态
    "-gaze",                    # 注视方向
    "-tracked",                 # 仅输出成功追踪的帧
    "-cam",                     # （可选）使用网络摄像头
    "-no_display"
]

# 运行命令
try:
    subprocess.run(command, check=True)
    print("✅ 面部特征提取完成，结果保存在:", OUTPUT_PATH)
except subprocess.CalledProcessError as e:
    print("❌ OpenFace 特征提取失败:", e)
