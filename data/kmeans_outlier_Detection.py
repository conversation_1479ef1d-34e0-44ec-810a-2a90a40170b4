import os
import pandas as pd
import numpy as np
from tqdm import tqdm
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
from sklearn.ensemble import IsolationForest

def global_kmeans_filter(folder_path, behavioral_features, n_clusters="auto", distance_threshold=3.0, output_csv="global_kmeans_outliers.csv"):
    """
    对所有样本做全局聚类，筛选距离聚类中心过远的异常样本。

    参数:
        folder_path (str): 包含 CSV 文件的文件夹路径。
        behavioral_features (list): 要提取的行为特征列。
        n_clusters (int): KMeans 聚类簇数。
        distance_threshold (float): 距离超过该标准差倍数将视为异常。
        output_csv (str): 输出的异常文件列表 CSV。
    """
    all_vectors = []
    filenames = []

    for fname in tqdm(os.listdir(folder_path), desc=f"提取 {os.path.basename(folder_path)} 特征"):
        if not fname.endswith(".csv"):
            continue
        csv_path = os.path.join(folder_path, fname)
        try:
            df = pd.read_csv(csv_path)[behavioral_features]
            df = df.replace([np.inf, -np.inf], np.nan).dropna()
            if df.shape[0] == 0:
                continue
            sample_vector = df.values.flatten()
            all_vectors.append(sample_vector)
            filenames.append(fname)
        except Exception as e:
            print(f"⚠️ 读取失败: {fname}, 原因: {e}")

    if not all_vectors:
        print(f"❌ 没有可用于聚类的样本：{folder_path}")
        return

    # 保证所有向量长度一致
    min_len = min(len(v) for v in all_vectors)
    all_vectors = [v[:min_len] for v in all_vectors]
    X = np.stack(all_vectors)

    # 标准化
    X = StandardScaler().fit_transform(X)

    # --- 自动选择最优聚类数 ---
    if n_clusters == "auto":
        silhouette_scores = []
        candidate_k = range(2, 10)
        for k in candidate_k:
            kmeans_k = KMeans(n_clusters=k, random_state=0).fit(X)
            score = silhouette_score(X, kmeans_k.labels_)
            silhouette_scores.append(score)
        best_k = candidate_k[np.argmax(silhouette_scores)]
        print(f"📌 使用轮廓系数法选择的最优聚类数: {best_k}")
    else:
        best_k = n_clusters

    # --- 执行最终聚类 ---
    kmeans = KMeans(n_clusters=best_k, random_state=0).fit(X)
    centers = kmeans.cluster_centers_
    labels = kmeans.labels_

    distances = np.linalg.norm(X - centers[labels], axis=1)
    avg_dist = distances.mean()
    std_dist = distances.std()
    threshold = avg_dist + distance_threshold * std_dist

    # 识别异常文件
    outlier_files = [f for f, d in zip(filenames, distances) if d > threshold]
    print(f"🔍 {folder_path} 异常文件数: {len(outlier_files)} / {len(filenames)}")

    pd.DataFrame({"文件": outlier_files}).to_csv(output_csv, index=False)
    print(f"✅ 异常文件保存至: {output_csv}")

def kmeans_isolation_forest_filter(folder_path, behavioral_features, n_clusters="auto",
                                 distance_threshold=3.0, contamination=0.1,
                                 output_csv="kmeans_isolation_outliers.csv"):
    """
    结合K-means和Isolation Forest进行二次异常检测筛选。

    参数:
        folder_path (str): 包含 CSV 文件的文件夹路径。
        behavioral_features (list): 要提取的行为特征列。
        n_clusters (int): KMeans 聚类簇数。
        distance_threshold (float): K-means距离超过该标准差倍数将视为异常。
        contamination (float): Isolation Forest异常比例参数 (0.05-0.2)。
        output_csv (str): 输出的异常文件列表 CSV。
    """
    all_vectors = []
    filenames = []

    # 第一步：数据收集和预处理
    for fname in tqdm(os.listdir(folder_path), desc=f"提取 {os.path.basename(folder_path)} 特征"):
        if not fname.endswith(".csv"):
            continue
        csv_path = os.path.join(folder_path, fname)
        try:
            df = pd.read_csv(csv_path)[behavioral_features]
            df = df.replace([np.inf, -np.inf], np.nan).dropna()
            if df.shape[0] == 0:
                continue
            sample_vector = df.values.flatten()
            all_vectors.append(sample_vector)
            filenames.append(fname)
        except Exception as e:
            print(f"⚠️ 读取失败: {fname}, 原因: {e}")

    if not all_vectors:
        print(f"❌ 没有可用于聚类的样本：{folder_path}")
        return

    # 保证所有向量长度一致
    min_len = min(len(v) for v in all_vectors)
    all_vectors = [v[:min_len] for v in all_vectors]
    X = np.stack(all_vectors)

    # 标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    print(f"📊 数据形状: {X_scaled.shape}, 样本数: {len(filenames)}")

    # 第二步：K-means聚类异常检测
    if n_clusters == "auto":
        silhouette_scores = []
        candidate_k = range(2, min(10, len(filenames)//2))
        for k in candidate_k:
            if k >= len(filenames):
                break
            kmeans_k = KMeans(n_clusters=k, random_state=0, n_init=10).fit(X_scaled)
            score = silhouette_score(X_scaled, kmeans_k.labels_)
            silhouette_scores.append(score)
        best_k = candidate_k[np.argmax(silhouette_scores)]
        print(f"📌 K-means最优聚类数: {best_k}")
    else:
        best_k = n_clusters

    # 执行K-means聚类
    kmeans = KMeans(n_clusters=best_k, random_state=0, n_init=10).fit(X_scaled)
    centers = kmeans.cluster_centers_
    labels = kmeans.labels_

    # 计算到聚类中心的距离
    distances = np.linalg.norm(X_scaled - centers[labels], axis=1)
    avg_dist = distances.mean()
    std_dist = distances.std()
    kmeans_threshold = avg_dist + distance_threshold * std_dist

    # K-means异常样本
    kmeans_outliers_mask = distances > kmeans_threshold
    kmeans_outlier_files = [f for f, is_outlier in zip(filenames, kmeans_outliers_mask) if is_outlier]

    print(f"🔍 K-means检测到异常文件数: {len(kmeans_outlier_files)} / {len(filenames)}")

    # 第三步：Isolation Forest二次筛选
    print("🌲 开始Isolation Forest二次筛选...")

    # 对所有样本应用Isolation Forest
    iso_forest = IsolationForest(
        contamination=contamination,
        random_state=42,
        n_estimators=100,
        max_samples='auto'
    )

    iso_predictions = iso_forest.fit_predict(X_scaled)
    iso_scores = iso_forest.decision_function(X_scaled)

    # -1表示异常，1表示正常
    iso_outliers_mask = iso_predictions == -1
    iso_outlier_files = [f for f, is_outlier in zip(filenames, iso_outliers_mask) if is_outlier]

    print(f"🌲 Isolation Forest检测到异常文件数: {len(iso_outlier_files)} / {len(filenames)}")

    # 第四步：结合两种方法的结果
    # 策略1：取并集（任一方法认为异常的都标记为异常）
    combined_outliers_union = set(kmeans_outlier_files) | set(iso_outlier_files)

    # 策略2：取交集（两种方法都认为异常的才标记为异常）
    combined_outliers_intersection = set(kmeans_outlier_files) & set(iso_outlier_files)

    # 策略3：加权评分（推荐）
    # 将K-means距离和Isolation Forest异常分数标准化后结合
    normalized_distances = (distances - distances.min()) / (distances.max() - distances.min())
    normalized_iso_scores = (iso_scores.max() - iso_scores) / (iso_scores.max() - iso_scores.min())

    # 加权组合分数（可调整权重）
    kmeans_weight = 0.6
    iso_weight = 0.4
    combined_scores = kmeans_weight * normalized_distances + iso_weight * normalized_iso_scores

    # 使用组合分数确定异常阈值
    score_threshold = np.percentile(combined_scores, 100 * (1 - contamination))
    combined_outliers_weighted = [f for f, score in zip(filenames, combined_scores) if score > score_threshold]

    # 输出结果统计
    print(f"\n📈 异常检测结果统计:")
    print(f"   K-means异常: {len(kmeans_outlier_files)}")
    print(f"   Isolation Forest异常: {len(iso_outlier_files)}")
    print(f"   并集策略异常: {len(combined_outliers_union)}")
    print(f"   交集策略异常: {len(combined_outliers_intersection)}")
    print(f"   加权策略异常: {len(combined_outliers_weighted)}")

    # 保存不同策略的结果
    base_name = output_csv.replace('.csv', '')

    # 保存K-means结果
    pd.DataFrame({"文件": kmeans_outlier_files}).to_csv(f"{base_name}_kmeans.csv", index=False)

    # 保存Isolation Forest结果
    pd.DataFrame({"文件": iso_outlier_files}).to_csv(f"{base_name}_isolation.csv", index=False)

    # 保存并集结果
    pd.DataFrame({"文件": list(combined_outliers_union)}).to_csv(f"{base_name}_union.csv", index=False)

    # 保存交集结果
    pd.DataFrame({"文件": list(combined_outliers_intersection)}).to_csv(f"{base_name}_intersection.csv", index=False)

    # 保存加权结果（推荐使用）
    pd.DataFrame({"文件": combined_outliers_weighted}).to_csv(output_csv, index=False)

    # 保存详细分析结果
    detailed_results = pd.DataFrame({
        "文件": filenames,
        "kmeans_distance": distances,
        "kmeans_outlier": kmeans_outliers_mask,
        "iso_score": iso_scores,
        "iso_outlier": iso_outliers_mask,
        "combined_score": combined_scores,
        "final_outlier": [f in combined_outliers_weighted for f in filenames]
    })
    detailed_results.to_csv(f"{base_name}_detailed.csv", index=False)

    print(f"\n✅ 异常检测结果已保存:")
    print(f"   主要结果(加权策略): {output_csv}")
    print(f"   详细分析结果: {base_name}_detailed.csv")
    print(f"   各策略对比: {base_name}_*.csv")

    return {
        'kmeans_outliers': kmeans_outlier_files,
        'isolation_outliers': iso_outlier_files,
        'union_outliers': list(combined_outliers_union),
        'intersection_outliers': list(combined_outliers_intersection),
        'weighted_outliers': combined_outliers_weighted,
        'detailed_results': detailed_results
    }

if __name__ == "__main__":
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]  # 根据需要修改特征名

    print("=" * 60)
    print("🚀 开始数据异常检测流程")
    print("=" * 60)

    # 方法1：原始K-means异常检测
    print("\n📊 方法1: 原始K-means异常检测")
    print("-" * 40)

    # 训练集异常检测
    train_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train"
    print(f"🔍 处理训练集: {train_path}")
    global_kmeans_filter(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters=4,
        distance_threshold=2.5,
        output_csv="train_kmeans_outliers.csv"
    )

    # 验证集异常检测
    val_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"
    print(f"🔍 处理验证集: {val_path}")
    global_kmeans_filter(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters=4,
        distance_threshold=2.5,
        output_csv="val_kmeans_outliers.csv"
    )

    # 方法2：K-means + Isolation Forest 二次筛选
    print("\n🌲 方法2: K-means + Isolation Forest 二次筛选")
    print("-" * 50)

    # 训练集二次筛选
    print(f"🔍 训练集二次筛选: {train_path}")
    train_results = kmeans_isolation_forest_filter(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters=4,
        distance_threshold=2.5,
        contamination=0.08,  # 预期异常比例8%
        output_csv="train_combined_outliers.csv"
    )

    # 验证集二次筛选
    print(f"🔍 验证集二次筛选: {val_path}")
    val_results = kmeans_isolation_forest_filter(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters=4,
        distance_threshold=2.5,
        contamination=0.08,  # 预期异常比例8%
        output_csv="val_combined_outliers.csv"
    )

    # 结果对比分析
    print("\n📈 结果对比分析")
    print("=" * 60)

    # 读取原始K-means结果
    try:
        train_kmeans_orig = pd.read_csv("train_kmeans_outliers.csv")
        val_kmeans_orig = pd.read_csv("val_kmeans_outliers.csv")

        train_combined = pd.read_csv("train_combined_outliers.csv")
        val_combined = pd.read_csv("val_combined_outliers.csv")

        print(f"训练集对比:")
        print(f"  原始K-means异常数: {len(train_kmeans_orig)}")
        print(f"  二次筛选异常数: {len(train_combined)}")
        print(f"  减少比例: {(len(train_kmeans_orig) - len(train_combined)) / len(train_kmeans_orig) * 100:.1f}%")

        print(f"\n验证集对比:")
        print(f"  原始K-means异常数: {len(val_kmeans_orig)}")
        print(f"  二次筛选异常数: {len(val_combined)}")
        print(f"  减少比例: {(len(val_kmeans_orig) - len(val_combined)) / len(val_kmeans_orig) * 100:.1f}%")

    except Exception as e:
        print(f"⚠️ 结果对比分析失败: {e}")

    print("\n✅ 异常检测流程完成!")
    print("💡 建议使用 train_combined_outliers.csv 和 val_combined_outliers.csv 作为最终的异常文件列表")
