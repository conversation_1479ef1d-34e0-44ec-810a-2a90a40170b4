import os
import pandas as pd
from tqdm import tqdm

# 原始 CSV 文件目录
csv_folder = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV"

# 目标 CSV 存放目录
output_csv_folder = "/data/gsd/ywj/dataset/Train"
os.makedirs(output_csv_folder, exist_ok=True)

# 需要保留的列
columns_to_keep = ["gaze_0_x", "gaze_0_y", "gaze_0_z", 
                    "gaze_1_x", "gaze_1_y", "gaze_1_z",
                    "gaze_angle_x", "gaze_angle_y", 
                    "pose_Tx", "pose_Ty", "pose_Tz",
                    "pose_Rx", "pose_Ry", "pose_Rz",
                    "AU01_r", "AU02_r", 
                    "AU04_r", "AU05_r", "AU06_r",
                    "AU07_r", "AU09_r", "AU10_r",
                    "AU12_r", "AU14_r", "AU15_r",
                    "AU17_r", "AU20_r", "AU23_r",
                    "AU25_r", "AU26_r", "AU45_r",
                    "AU01_c", "AU02_c", "AU04_c", 
                    "AU05_c", "AU06_c", "AU07_c", 
                    "AU09_c", "AU10_c", "AU12_c", 
                    "AU14_c", "AU15_c", "AU17_c", 
                    "AU20_c", "AU23_c", "AU25_c", 
                    "AU26_c", "AU28_c", "AU45_c"]

# 遍历所有子文件夹（Train, Test, Validation）
for subset in ["Train"]:
    input_subset_folder = os.path.join(csv_folder, subset)
    output_subset_folder = os.path.join(output_csv_folder, subset)
    
    # 创建新目录（如果不存在）
    os.makedirs(output_subset_folder, exist_ok=True)

    # 确保原始数据集文件夹存在
    if not os.path.exists(input_subset_folder):
        continue

    # 遍历 CSV 文件
    for file_name in tqdm(os.listdir(input_subset_folder), desc=f"Processing {subset}", unit="file"):
        if file_name.endswith(".csv"):  # 只处理 CSV 文件
            input_file_path = os.path.join(input_subset_folder, file_name)
            output_file_path = os.path.join(output_subset_folder, file_name)

            try:
                # 读取 CSV
                df = pd.read_csv(input_file_path)

                # 只保留指定列，丢弃其他列
                df = df[columns_to_keep]

                # 另存到新的目录
                df.to_csv(output_file_path, index=False)

            except Exception as e:
                print(f"Error processing {file_name}: {e}")