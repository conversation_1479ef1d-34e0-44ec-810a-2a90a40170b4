import os  # 用于路径处理和创建文件夹等操作
import subprocess  # 用于运行外部命令（如调用 ffmpeg 和 OpenFace）
import cv2  # OpenCV，用于视频读取和处理
import pandas as pd  # 用于处理 CSV 文件
from tqdm import tqdm  # 用于显示进度条

# OpenFace 的 FeatureExtraction 可执行文件路径
openface_path = "/data/gsd/czx/project/OpenFace-master/build/bin/FeatureExtraction"

# EngageNet 数据集的路径
train_folder = "/data/gsd/czx/Dataset/EngageNet_Processed/Train/"
test_folder = "/data/gsd/czx/Dataset/EngageNet_Processed/Test/"
validation_folder = "/data/gsd/czx/Dataset/EngageNet_Processed/Validation/"

# 设置目标帧率和每个视频处理的帧数
target_fps = 30  # 目标帧率为 30 FPS
target_frames = 280  # 每段视频最多保留 280 帧

# 设置输出文件夹路径
output_folder = "/data/gsd/ywj/dataset/"
os.makedirs(os.path.join(output_folder, "Train"), exist_ok=True)  # 创建训练集输出目录
os.makedirs(os.path.join(output_folder, "Test"), exist_ok=True)  # 创建测试集输出目录
os.makedirs(os.path.join(output_folder, "Validation"), exist_ok=True)  # 创建验证集输出目录

# 限制 OpenBLAS 使用的线程数，防止多线程冲突
os.environ["OPENBLAS_NUM_THREADS"] = "1"

def adjust_fps(video_path, target_fps):
    """调整视频帧率到目标值（若原始帧率较高）"""
    cap = cv2.VideoCapture(video_path)  # 读取视频
    fps = int(cap.get(cv2.CAP_PROP_FPS))  # 获取原始帧率
    cap.release()  # 释放视频资源

    if fps > target_fps:
        # 如果帧率高于目标值，则进行调整
        output_path = video_path.replace(".mp4", "_adjusted.mp4")  # 输出文件名
        command = [
            "ffmpeg", "-y", "-i", video_path,  # 输入文件
            "-r", str(target_fps),  # 设置新帧率
            "-vsync", "0",  # 禁止自动丢帧
            output_path  # 输出路径
        ]
        # 调用 ffmpeg 进行转换，隐藏输出信息
        subprocess.run(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return output_path  # 返回新视频路径
    return video_path  # 若无需调整，返回原始路径

def process_videos(input_folder, output_folder, is_training=True):
    """处理指定文件夹中的所有视频，提取 OpenFace 特征"""
    video_list = [v for v in os.listdir(input_folder) if v.endswith(".mp4")]  # 获取所有 mp4 视频

    for video_name in tqdm(video_list, desc=f"Processing {input_folder}", unit="video"):
        video_path = os.path.join(input_folder, video_name)  # 构建完整视频路径
        output_csv_path = os.path.join(output_folder, video_name.replace(".mp4", ".csv"))  # 输出 CSV 路径

        # 调整帧率
        adjusted_video_path = adjust_fps(video_path, target_fps)
        if not os.path.exists(adjusted_video_path):  # 如果调整后的视频不存在，跳过
            print(f"Error adjusting FPS: {video_path}")
            continue

        # 构建 OpenFace 命令以提取特征
        command = [
            openface_path, "-f", adjusted_video_path,  # 指定输入视频
            "-out_dir", output_folder,  # 输出目录
            "-of", output_csv_path.replace(".csv", ""),  # 输出文件（无扩展名）
            "-pose", "-aus", "-gaze"  # 提取头部姿态、动作单元、眼动特征
        ]
        # 执行命令，获取返回值和输出
        result = subprocess.run(command, capture_output=True, text=True, env={"OPENBLAS_NUM_THREADS": "1"})
        if result.returncode != 0:  # 如果执行失败，跳过
            print(f"Error processing video: {video_path}")
            continue

        # 确保输出 CSV 存在
        if not os.path.exists(output_csv_path):
            print(f"CSV file not generated: {output_csv_path}")
            continue

        try:
            df = pd.read_csv(output_csv_path)  # 读取 CSV
            # 如果为空或列数不对，删除
            if df.empty or len(df.columns) < 10:
                print(f"Warning: Corrupt CSV {output_csv_path}, removing it.")
                os.remove(output_csv_path)
                continue
        except pd.errors.EmptyDataError:  # 若读取出错
            print(f"Error: Empty CSV file {output_csv_path}, removing it.")
            os.remove(output_csv_path)
            continue

        # 调整帧数至 target_frames
        num_frames = len(df)
        if num_frames > target_frames:
            df = df.iloc[:target_frames]  # 截断前 target_frames 帧
        elif num_frames < target_frames:
            last_frame = df.iloc[-1]  # 取最后一帧
            missing_frames = target_frames - num_frames  # 计算缺失帧数
            # 重复最后一帧补齐
            df = pd.concat([df, pd.DataFrame([last_frame] * missing_frames)], ignore_index=True)

        # 只保留感兴趣的特征列（眼动、姿态、AU等）
        columns_to_keep = ["gaze_0_x", "gaze_0_y", "gaze_0_z", 
                           "gaze_1_x", "gaze_1_y", "gaze_1_z",
                           "gaze_angle_x", "gaze_angle_y", 
                           "pose_Tx", "pose_Ty", "pose_Tz",
                           "pose_Rx", "pose_Ry", "pose_Rz",
                           "AU01_r", "AU02_r", 
                           "AU04_r", "AU05_r", "AU06_r",
                           "AU07_r", "AU09_r", "AU10_r",
                           "AU12_r", "AU14_r", "AU15_r",
                           "AU17_r", "AU20_r", "AU23_r",
                           "AU25_r", "AU26_r", "AU45_r",
                           "AU01_c", "AU02_c", "AU04_c", 
                           "AU05_c", "AU06_c", "AU07_c", 
                           "AU09_c", "AU10_c", "AU12_c", 
                           "AU14_c", "AU15_c", "AU17_c", 
                           "AU20_c", "AU23_c", "AU25_c", 
                           "AU26_c", "AU28_c", "AU45_c"]
        df = df[columns_to_keep]  # 筛选这些列

        df.to_csv(output_csv_path, index=False)  # 保存为新的 CSV 文件

# 分别处理训练集、测试集和验证集
process_videos(train_folder, os.path.join(output_folder, "Train"), is_training=True)
# process_videos(test_folder, os.path.join(output_folder, "Test"), is_training=False)
# process_videos(validation_folder, os.path.join(output_folder, "Validation"), is_training=False)
