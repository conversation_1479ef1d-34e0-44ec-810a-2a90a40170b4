import pywt  # 导入 pywt 库，用于连续小波变换 (CWT)
import numpy as np  # 导入 numpy 库，用于数值计算
import torch  # 导入 PyTorch 库，用于张量操作


def batch_cwt(batch_signals, frequencies, sampling_frequency):
    """
    计算一批信号的连续小波变换 (CWT)。

    参数:
        batch_signals (torch.Tensor): 输入信号批次，形状为 [batch_size, channels, features, signal_length]。
        frequencies (np.array): 用于 CWT 的频率数组。
        sampling_frequency (int): 信号的采样频率。

    返回:
        torch.Tensor: 包含输入信号批次 CWT 系数的张量。
    """
        
    # 从 batch_signals 的形状中提取批次大小和特征数量
    batch_size, _, features, _ = batch_signals.shape
    cwt_batch = []  # 用于存储每个批次的 CWT 结果

    # 遍历批次中的每个信号，计算其 CWT
    for i in range(batch_size):
        cwt_features = []  # 用于存储单个信号的所有特征的 CWT 结果
        for j in range(features):
            # 提取信号，确保其在 CPU 上并展平为 1D
            signal = batch_signals[i, :, j, :].squeeze().cpu().detach().numpy()

            # 使用指定的小波函数和尺度计算 CWT
            coefficients, _ = pywt.cwt(signal, frequencies, 'cmor1.5-1.0', sampling_period=1/sampling_frequency)
            coefficients = np.abs(coefficients)  # 取绝对值，得到 CWT 系数的幅值

            # 收集每个特征的 CWT 结果
            cwt_features.append(coefficients)

        # 将单个批次项的所有特征的 CWT 结果堆叠在一起
        cwt_features_stacked = np.stack(cwt_features, axis=0)
        cwt_batch.append(cwt_features_stacked)

    # 将 numpy 数组列表转换为单个 numpy 数组
    cwt_batch_np = np.array(cwt_batch)
    
    # 将 numpy 数组转换为 PyTorch 张量，以便与 PyTorch 操作兼容
    cwt_batch_tensor = torch.tensor(cwt_batch_np, dtype=torch.float)

    return cwt_batch_tensor  # 返回 CWT 系数的张量