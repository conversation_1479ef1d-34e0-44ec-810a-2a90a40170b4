#!/usr/bin/env python3
"""
高级异常检测脚本：结合K-means和Isolation Forest进行数据预处理
作者: AI Assistant
功能: 在K-means聚类基础上使用Isolation Forest进行二次筛选，提高异常检测的准确性
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from sklearn.cluster import KMeans
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

def visualize_outlier_detection(X_scaled, filenames, kmeans_outliers, iso_outliers, 
                               combined_outliers, output_dir="outlier_analysis"):
    """
    可视化异常检测结果
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 使用PCA降维到2D进行可视化
    pca = PCA(n_components=2, random_state=42)
    X_2d = pca.fit_transform(X_scaled)
    
    # 创建标签
    labels = []
    for i, fname in enumerate(filenames):
        if fname in combined_outliers:
            labels.append('Combined Outlier')
        elif fname in kmeans_outliers:
            labels.append('K-means Only')
        elif fname in iso_outliers:
            labels.append('Isolation Forest Only')
        else:
            labels.append('Normal')
    
    # 绘制散点图
    plt.figure(figsize=(12, 8))
    colors = {'Normal': 'blue', 'K-means Only': 'orange', 
              'Isolation Forest Only': 'green', 'Combined Outlier': 'red'}
    
    for label in colors:
        mask = [l == label for l in labels]
        if any(mask):
            plt.scatter(X_2d[mask, 0], X_2d[mask, 1], 
                       c=colors[label], label=label, alpha=0.7, s=50)
    
    plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
    plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
    plt.title('异常检测结果可视化 (PCA降维)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'outlier_detection_visualization.png'), dpi=300)
    plt.close()
    
    # 绘制异常检测方法对比
    methods = ['K-means', 'Isolation Forest', 'Combined']
    counts = [len(kmeans_outliers), len(iso_outliers), len(combined_outliers)]
    
    plt.figure(figsize=(10, 6))
    bars = plt.bar(methods, counts, color=['skyblue', 'lightgreen', 'salmon'])
    plt.title('不同异常检测方法的异常样本数量对比')
    plt.ylabel('异常样本数量')
    
    # 在柱状图上添加数值标签
    for bar, count in zip(bars, counts):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'method_comparison.png'), dpi=300)
    plt.close()
    
    print(f"📊 可视化结果已保存到 {output_dir} 目录")

def analyze_feature_importance(X_scaled, behavioral_features, outlier_mask, output_dir="outlier_analysis"):
    """
    分析哪些特征对异常检测最重要
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 计算正常样本和异常样本的特征统计
    normal_data = X_scaled[~outlier_mask]
    outlier_data = X_scaled[outlier_mask]
    
    if len(outlier_data) == 0:
        print("⚠️ 没有异常样本，跳过特征重要性分析")
        return
    
    # 计算特征差异
    normal_mean = np.mean(normal_data, axis=0)
    outlier_mean = np.mean(outlier_data, axis=0)
    feature_diff = np.abs(outlier_mean - normal_mean)
    
    # 创建特征重要性DataFrame
    feature_importance = pd.DataFrame({
        'Feature': behavioral_features,
        'Normal_Mean': normal_mean,
        'Outlier_Mean': outlier_mean,
        'Difference': feature_diff
    })
    feature_importance = feature_importance.sort_values('Difference', ascending=False)
    
    # 绘制特征重要性图
    plt.figure(figsize=(12, 8))
    plt.barh(range(len(behavioral_features)), feature_importance['Difference'])
    plt.yticks(range(len(behavioral_features)), feature_importance['Feature'])
    plt.xlabel('特征差异 (|异常均值 - 正常均值|)')
    plt.title('特征对异常检测的重要性')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_importance.png'), dpi=300)
    plt.close()
    
    # 保存特征重要性分析结果
    feature_importance.to_csv(os.path.join(output_dir, 'feature_importance.csv'), index=False)
    print(f"📈 特征重要性分析结果已保存到 {output_dir}/feature_importance.csv")
    
    return feature_importance

def comprehensive_outlier_detection(folder_path, behavioral_features, 
                                  n_clusters="auto", distance_threshold=2.5, 
                                  contamination=0.08, output_prefix="comprehensive"):
    """
    综合异常检测：结合多种方法和可视化分析
    """
    print(f"🔍 开始综合异常检测: {folder_path}")
    print(f"📋 使用特征: {behavioral_features}")
    
    # 数据加载和预处理
    all_vectors = []
    filenames = []
    
    for fname in tqdm(os.listdir(folder_path), desc="加载数据"):
        if not fname.endswith(".csv"):
            continue
        csv_path = os.path.join(folder_path, fname)
        try:
            df = pd.read_csv(csv_path)[behavioral_features]
            df = df.replace([np.inf, -np.inf], np.nan).dropna()
            if df.shape[0] == 0:
                continue
            
            # 计算多种统计特征
            sample_vector = []
            for col in behavioral_features:
                col_data = df[col].values
                sample_vector.extend([
                    np.mean(col_data),      # 均值
                    np.std(col_data),       # 标准差
                    np.median(col_data),    # 中位数
                    np.percentile(col_data, 25),  # 25%分位数
                    np.percentile(col_data, 75),  # 75%分位数
                    np.max(col_data) - np.min(col_data),  # 范围
                ])
            
            all_vectors.append(sample_vector)
            filenames.append(fname)
        except Exception as e:
            print(f"⚠️ 读取失败: {fname}, 原因: {e}")
    
    if not all_vectors:
        print(f"❌ 没有可用数据: {folder_path}")
        return None
    
    X = np.array(all_vectors)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    print(f"📊 数据形状: {X_scaled.shape}, 样本数: {len(filenames)}")
    
    # K-means异常检测
    print("🎯 执行K-means异常检测...")
    if n_clusters == "auto":
        best_k = min(8, max(2, len(filenames) // 10))
        print(f"📌 自动选择聚类数: {best_k}")
    else:
        best_k = n_clusters
    
    kmeans = KMeans(n_clusters=best_k, random_state=42, n_init=10).fit(X_scaled)
    distances = np.linalg.norm(X_scaled - kmeans.cluster_centers_[kmeans.labels_], axis=1)
    kmeans_threshold = np.mean(distances) + distance_threshold * np.std(distances)
    kmeans_outliers = [f for f, d in zip(filenames, distances) if d > kmeans_threshold]
    
    # Isolation Forest异常检测
    print("🌲 执行Isolation Forest异常检测...")
    iso_forest = IsolationForest(contamination=contamination, random_state=42, n_estimators=200)
    iso_predictions = iso_forest.fit_predict(X_scaled)
    iso_outliers = [f for f, pred in zip(filenames, iso_predictions) if pred == -1]
    
    # 组合结果
    combined_outliers = list(set(kmeans_outliers) | set(iso_outliers))
    
    print(f"\n📈 检测结果:")
    print(f"   K-means异常: {len(kmeans_outliers)}")
    print(f"   Isolation Forest异常: {len(iso_outliers)}")
    print(f"   组合结果异常: {len(combined_outliers)}")
    
    # 保存结果
    results = {
        'kmeans_outliers': kmeans_outliers,
        'isolation_outliers': iso_outliers,
        'combined_outliers': combined_outliers,
        'filenames': filenames,
        'X_scaled': X_scaled
    }
    
    # 保存异常文件列表
    pd.DataFrame({"文件": combined_outliers}).to_csv(f"{output_prefix}_outliers.csv", index=False)
    
    # 可视化分析
    visualize_outlier_detection(X_scaled, filenames, kmeans_outliers, 
                               iso_outliers, combined_outliers)
    
    # 特征重要性分析
    outlier_mask = np.array([f in combined_outliers for f in filenames])
    feature_names = []
    for feat in behavioral_features:
        feature_names.extend([f"{feat}_mean", f"{feat}_std", f"{feat}_median", 
                            f"{feat}_q25", f"{feat}_q75", f"{feat}_range"])
    
    analyze_feature_importance(X_scaled, feature_names, outlier_mask)
    
    print(f"✅ 综合异常检测完成，结果保存为 {output_prefix}_outliers.csv")
    
    return results

if __name__ == "__main__":
    # 配置参数
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    
    # 数据路径
    train_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train"
    val_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"
    
    print("🚀 开始高级异常检测流程")
    print("=" * 60)
    
    # 训练集异常检测
    if os.path.exists(train_path):
        print(f"\n📂 处理训练集: {train_path}")
        train_results = comprehensive_outlier_detection(
            folder_path=train_path,
            behavioral_features=behavioral_features,
            n_clusters="auto",
            distance_threshold=2.5,
            contamination=0.08,
            output_prefix="train_advanced"
        )
    else:
        print(f"⚠️ 训练集路径不存在: {train_path}")
    
    # 验证集异常检测
    if os.path.exists(val_path):
        print(f"\n📂 处理验证集: {val_path}")
        val_results = comprehensive_outlier_detection(
            folder_path=val_path,
            behavioral_features=behavioral_features,
            n_clusters="auto",
            distance_threshold=2.5,
            contamination=0.08,
            output_prefix="val_advanced"
        )
    else:
        print(f"⚠️ 验证集路径不存在: {val_path}")
    
    print("\n✅ 高级异常检测流程完成!")
    print("📊 请查看生成的可视化图表和分析结果")
