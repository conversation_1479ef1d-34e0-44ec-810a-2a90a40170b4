class EarlyStopping:
    """
    用于在验证损失不再改善时提前停止训练的类。

    属性:
        patience (int): 最后一次改善后等待的 epoch 数量。
        verbose (bool): 如果为 True，则在每次验证损失改善时打印消息。
        delta (float): 监控指标的最小变化量，以认定为改善。
        trace_func (callable): 用于打印进度消息的函数，默认为 `print`。
        counter (int): 自上次改善以来的 epoch 计数。
        best_score (float): 监控指标中观察到的最佳分数。
        early_stop (bool): 如果为 True，则在下次检查时停止训练。
        test_accuracy_max (float): 观察到的最大测试准确率。
    """

    def __init__(self, patience=0, verbose=False, delta=0, trace_func=print):
        """
        初始化 EarlyStopping 实例。

        参数:
            patience (int): 在训练停止之前允许的无改善 epoch 数量。
            verbose (bool): 启用输出消息以指示改善。
            delta (float): 认定为改善的最小变化量。
            trace_func (callable): 用于输出消息的函数。
        """
        self.patience = patience  # 设置 patience 参数
        self.verbose = verbose  # 设置 verbose 参数
        self.delta = delta  # 设置 delta 参数
        self.trace_func = trace_func  # 设置 trace_func 参数
        self.counter = 0  # 初始化 counter 为 0
        self.best_score = None  # 初始化 best_score 为 None
        self.early_stop = False  # 初始化 early_stop 为 False
        self.test_accuracy_max = 0  # 初始化 test_accuracy_max 为 0

    def __call__(self, test_accuracy):
        """
        评估当前的测试准确率并更新停止条件。

        参数:
            test_accuracy (float): 当前 epoch 的测试准确率。
        """
        score = test_accuracy  # 将当前测试准确率赋值给 score

        if self.best_score is None:
            # 如果尚未记录最佳分数，则将当前分数设置为最佳分数
            self.best_score = score
            self.improvement_detected(test_accuracy)  # 调用 improvement_detected 方法
        elif score < self.best_score + self.delta:
            # 未检测到改善，增加 counter
            self.counter += 1
            if self.verbose:
                self.trace_func(f'EarlyStopping counter: {self.counter} out of {self.patience}')  # 打印 counter 信息
            if self.counter >= self.patience:
                # 如果 counter 超过 patience，则停止训练
                self.early_stop = True
        else:
            # 检测到改善，重置 counter
            self.best_score = score
            self.improvement_detected(test_accuracy)  # 调用 improvement_detected 方法
            self.counter = 0  # 重置 counter

    def improvement_detected(self, test_accuracy):
        """
        处理改善检测，更新最大测试准确率并可能记录日志。

        参数:
            test_accuracy (float): 当前的测试准确率。
        """
        if self.verbose:
            self.trace_func(f'Test accuracy increased to {test_accuracy:.6f}, resetting patience.')  # 打印改善信息
        self.test_accuracy_max = test_accuracy  # 更新最大测试准确率