import pandas as pd
import matplotlib.pyplot as plt

df = pd.read_csv("/data/gsd/ywj/dataset/EngageNet_CSV/Train/subject_2_thw33jke4j_vid_1_20.csv")
df.columns = df.columns.str.strip()

aus = ['pose_Tx', 'pose_Ty']
df[aus].plot(figsize=(10, 4))

plt.title("Head posture over Time(Highly-Engaged)")
plt.xlabel("Frame Index")
plt.ylabel("pose Intensity")
plt.grid(True)
plt.tight_layout()

# 保存到本地文件
plt.savefig("/data/gsd/ywj/dataset/Highly-Engaged_pose_plot_subject_2_thw33jke4j_vid_1_20.png")  # 可以指定路径
plt.close()
