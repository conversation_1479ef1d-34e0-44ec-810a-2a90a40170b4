# 异常检测系统使用指南

本项目提供了基于K-means聚类和Isolation Forest的二次筛选异常检测系统，用于数据预处理和质量控制。

## 🚀 功能特性

### 1. 多种检测方法
- **K-means聚类检测**: 基于聚类中心距离的异常检测
- **Isolation Forest检测**: 基于随机森林的异常检测
- **二次筛选**: 结合两种方法的优势，提高检测准确性

### 2. 灵活的组合策略
- **并集策略**: 任一方法检测到的异常都标记为异常
- **交集策略**: 两种方法都检测到的才标记为异常
- **加权策略**: 根据权重组合两种方法的分数（推荐）

### 3. 可视化分析
- PCA降维可视化异常分布
- 特征重要性分析
- 方法对比图表

## 📁 文件结构

```
data/
├── kmeans_outlier_Detection.py          # 原始K-means + 新增二次筛选功能
├── advanced_outlier_detection.py        # 高级异常检测（包含可视化）
├── configurable_outlier_detection.py    # 可配置异常检测系统
run_outlier_detection.py                 # 主运行脚本
outlier_detection_config.json            # 配置文件
```

## 🔧 使用方法

### 方法1: 快速开始（推荐）

```bash
python run_outlier_detection.py
```

运行后会提示选择检测方法：
1. 仅使用K-means聚类检测
2. 使用K-means + Isolation Forest二次筛选（推荐）
3. 运行两种方法并对比结果

### 方法2: 使用原始脚本

```bash
python data/kmeans_outlier_Detection.py
```

这会运行原始K-means检测和新的二次筛选功能。

### 方法3: 高级检测（包含可视化）

```bash
python data/advanced_outlier_detection.py
```

提供更详细的分析和可视化结果。

### 方法4: 可配置检测

```bash
python data/configurable_outlier_detection.py
```

使用配置文件自定义所有参数。

## ⚙️ 配置参数

### 主要参数说明

#### K-means参数
- `n_clusters`: 聚类数量，"auto"为自动选择
- `distance_threshold`: 距离阈值倍数（默认2.5）
- `random_state`: 随机种子

#### Isolation Forest参数
- `contamination`: 预期异常比例（0.05-0.2，默认0.08）
- `n_estimators`: 树的数量（默认200）
- `max_samples`: 每棵树的样本数

#### 组合策略参数
- `method`: 组合方法（"weighted", "union", "intersection"）
- `kmeans_weight`: K-means权重（默认0.6）
- `isolation_weight`: Isolation Forest权重（默认0.4）

### 修改配置

编辑 `outlier_detection_config.json` 文件：

```json
{
    "behavioral_features": [
        "pose_Tx", "pose_Ty", "AU17_r", "AU26_r"
    ],
    "kmeans_params": {
        "n_clusters": "auto",
        "distance_threshold": 2.5
    },
    "isolation_forest_params": {
        "contamination": 0.08,
        "n_estimators": 200
    },
    "combination_strategy": {
        "method": "weighted",
        "kmeans_weight": 0.6,
        "isolation_weight": 0.4
    }
}
```

## 📊 输出文件说明

### 主要结果文件
- `train_combined_outliers.csv`: 训练集异常文件列表（推荐使用）
- `val_combined_outliers.csv`: 验证集异常文件列表（推荐使用）

### 详细分析文件
- `*_detailed.csv`: 包含每个文件的详细分数和标记
- `*_kmeans.csv`: 仅K-means检测结果
- `*_isolation.csv`: 仅Isolation Forest检测结果
- `*_union.csv`: 并集策略结果
- `*_intersection.csv`: 交集策略结果

### 可视化文件（如果启用）
- `outlier_analysis/outlier_detection_visualization.png`: 异常分布可视化
- `outlier_analysis/method_comparison.png`: 方法对比图
- `outlier_analysis/feature_importance.png`: 特征重要性图

## 🔄 集成到训练流程

修改后的数据加载器会自动使用二次筛选结果：

```python
# data/data_loader.py 已自动更新
# 优先使用二次筛选结果，如果不存在则使用原始K-means结果
train_data, train_labels = load_csv_data(
    train_folder_path, 
    label_file, 
    behavioral_features, 
    exclude_list_path="train_combined_outliers.csv"  # 自动选择最佳结果
)
```

## 📈 效果评估

### 异常检测质量指标
1. **检测数量**: 异常样本数量和比例
2. **方法一致性**: 不同方法检测结果的重叠度
3. **特征重要性**: 哪些特征对异常检测最重要
4. **聚类质量**: 轮廓系数等聚类评估指标

### 建议的评估流程
1. 运行对比分析（方法3）
2. 查看可视化结果，确认异常分布合理
3. 检查特征重要性，确认符合预期
4. 根据下游任务性能调整参数

## 🛠️ 故障排除

### 常见问题

1. **数据路径错误**
   - 检查配置文件中的路径设置
   - 确认数据文件存在且格式正确

2. **特征列缺失**
   - 检查CSV文件是否包含所需的行为特征列
   - 确认列名拼写正确

3. **内存不足**
   - 减少`n_estimators`参数
   - 使用`max_samples`限制每棵树的样本数

4. **检测结果异常**
   - 调整`contamination`参数
   - 修改`distance_threshold`参数
   - 尝试不同的组合策略

### 参数调优建议

- **contamination**: 从0.05开始，根据实际异常比例调整
- **distance_threshold**: 从2.0开始，逐步增加到3.0
- **组合权重**: K-means权重0.5-0.7，Isolation Forest权重0.3-0.5

## 📞 技术支持

如有问题，请检查：
1. 日志文件 `outlier_detection.log`
2. 详细分析文件中的统计信息
3. 可视化结果是否符合预期

建议在使用前先在小数据集上测试参数设置。
