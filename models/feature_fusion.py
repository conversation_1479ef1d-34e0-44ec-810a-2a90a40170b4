from models.temporal_frequency_stream import TF_ConvModule
from models.temporal_spatial_stream import TS_Stream

import torch
from torch import nn
from torch.backends import cudnn

cudnn.benchmark = False
cudnn.deterministic = True

class MultiHeadCrossModalAttention(nn.Module):  # 👉 新增：定义多头交互注意力模块 双向交互注意力机制（TS ↔ TF）
    """
    多头交互注意力模块：通用版，支持任意 query 和 key/value 输入
    """
    def __init__(self, dim, num_heads=4):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        assert dim % num_heads == 0, "proj_dim must be divisible by num_heads"

        self.query_proj = nn.Linear(dim, dim)
        self.key_proj = nn.Linear(dim, dim)
        self.value_proj = nn.Linear(dim, dim)
        self.scale = self.head_dim ** -0.5

        self.out_proj = nn.Linear(dim, dim)  # 输出投影

    def forward(self, query_feat, key_value_feat):
        B = query_feat.size(0)
        Q = self.query_proj(query_feat).view(B, self.num_heads, self.head_dim)  # (B, H, D)
        K = self.key_proj(key_value_feat).view(B, self.num_heads, self.head_dim)
        V = self.value_proj(key_value_feat).view(B, self.num_heads, self.head_dim)

        attn_score = (Q * K).sum(dim=-1, keepdim=True) * self.scale  # (B, H, 1)
        attn = torch.softmax(attn_score, dim=1)  # (B, H, 1)
        attn_output = (attn * V).view(B, -1)  # 合并注意力头输出 (B, dim)

        return query_feat + self.out_proj(attn_output)  # 残差连接


class Decision_Fusion(nn.Module):
    """
    特征级融合版本的决策模块：使用双向多头交互注意力进行 TS↔TF 融合。

    参数:
        n_classes (int): 分类任务的类别数量。
    """

    def __init__(self, n_classes, proj_dim=128, num_heads=4):
        super(Decision_Fusion, self).__init__()

        # 两个流的特征提取网络
        self.TS_Stream = TS_Stream()
        self.TF_ConvModule = TF_ConvModule()
        self.n_classes = n_classes
        self.proj_dim = proj_dim

        # 使用 dummy 输入推理出特征维度
        dummy_TS = torch.randn(1, 1, 4, 280)
        dummy_TF = torch.randn(1, 4, 20, 280)
        with torch.no_grad():
            TS_feat = self.TS_Stream(dummy_TS)
            TF_feat = self.TF_ConvModule(dummy_TF)
            ts_dim = TS_feat.view(1, -1).size(1)
            tf_dim = TF_feat.view(1, -1).size(1)

        # 👉 添加投影模块
        self.TS_projector = nn.Linear(ts_dim, proj_dim)
        self.TF_projector = nn.Linear(tf_dim, proj_dim)

        # 👉 新增：双向多头注意力模块
        self.attn_TS_to_TF = MultiHeadCrossModalAttention(proj_dim, num_heads)
        self.attn_TF_to_TS = MultiHeadCrossModalAttention(proj_dim, num_heads)

        # 👉 输出分类器（投影后的双向拼接，维度为 2 * proj_dim）
        self.fusion_head = nn.Sequential(
            nn.Linear(2 * proj_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, n_classes)
        )

    def forward(self, TS_input, TF_input):
        # 提取两个模态的中间特征
        TS_feature = self.TS_Stream(TS_input)
        TF_feature = self.TF_ConvModule(TF_input)

        # 展平特征向量
        TS_feature = TS_feature.view(TS_feature.size(0), -1)
        TF_feature = TF_feature.view(TF_feature.size(0), -1)

        # 投影到统一维度
        TS_proj = self.TS_projector(TS_feature)
        TF_proj = self.TF_projector(TF_feature)

        # 👉 双向注意力交互
        TS_fused = self.attn_TS_to_TF(TS_proj, TF_proj)  # TS ← TF
        TF_fused = self.attn_TF_to_TS(TF_proj, TS_proj)  # TF ← TS

        # 👉 拼接双向融合结果
        fused_feature = torch.cat([TS_fused, TF_fused], dim=1)  # (B, 2 * proj_dim)

        # 保证 fused_feature 与 fusion_head 权重同设备
        fused_feature = fused_feature.to(self.fusion_head[0].weight.device)

        # 分类输出
        output = self.fusion_head(fused_feature)
        return output